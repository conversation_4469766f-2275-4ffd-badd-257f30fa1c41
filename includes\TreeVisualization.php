<?php
/**
 * Tree Visualization Helper
 * MLM Binary Plan System
 */

class TreeVisualization {
    
    /**
     * Generate HTML for binary tree visualization
     */
    public static function renderTree($treeData, $currentUserId = null) {
        if (empty($treeData)) {
            return '<div class="text-center text-muted">No tree data available</div>';
        }
        
        $html = '<div class="tree-container">';
        $html .= self::renderNode($treeData, $currentUserId, 0);
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render individual tree node
     */
    private static function renderNode($node, $currentUserId, $level) {
        if (empty($node)) {
            return '';
        }
        
        $isCurrentUser = $node['user_id'] === $currentUserId;
        $statusClass = $node['status'] === 'active' ? 'active-user' : 'inactive-user';
        $currentClass = $isCurrentUser ? 'current-user' : '';
        
        $html = '<div class="tree-level level-' . $level . '">';
        
        // Node container
        $html .= '<div class="tree-node-container">';
        
        // Node
        $html .= '<div class="tree-node ' . $statusClass . ' ' . $currentClass . '" data-user-id="' . $node['user_id'] . '">';
        $html .= '<div class="node-header">';
        $html .= '<strong>' . htmlspecialchars($node['user_id']) . '</strong>';
        $html .= '</div>';
        $html .= '<div class="node-body">';
        $html .= '<div class="user-name">' . htmlspecialchars($node['full_name']) . '</div>';
        $html .= '<div class="user-status">';
        $html .= '<span class="status-badge status-' . $node['status'] . '">' . ucfirst($node['status']) . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Children container
        if (!empty($node['left']) || !empty($node['right'])) {
            $html .= '<div class="tree-line"></div>';
            $html .= '<div class="tree-children">';
            
            // Left child
            $html .= '<div class="tree-child left-child">';
            if (!empty($node['left'])) {
                $html .= '<div class="child-label">Left</div>';
                $html .= self::renderNode($node['left'], $currentUserId, $level + 1);
            } else {
                $html .= '<div class="empty-slot" data-parent="' . $node['user_id'] . '" data-side="left">';
                $html .= '<i class="fas fa-plus-circle"></i><br>Available';
                $html .= '</div>';
            }
            $html .= '</div>';
            
            // Right child
            $html .= '<div class="tree-child right-child">';
            if (!empty($node['right'])) {
                $html .= '<div class="child-label">Right</div>';
                $html .= self::renderNode($node['right'], $currentUserId, $level + 1);
            } else {
                $html .= '<div class="empty-slot" data-parent="' . $node['user_id'] . '" data-side="right">';
                $html .= '<i class="fas fa-plus-circle"></i><br>Available';
                $html .= '</div>';
            }
            $html .= '</div>';
            
            $html .= '</div>'; // tree-children
        }
        
        $html .= '</div>'; // tree-node-container
        $html .= '</div>'; // tree-level
        
        return $html;
    }
    
    /**
     * Generate CSS for tree visualization
     */
    public static function getTreeCSS() {
        return '
        <style>
        .tree-container {
            overflow-x: auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-family: Arial, sans-serif;
        }
        
        .tree-level {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .tree-node-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .tree-node {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem;
            text-align: center;
            min-width: 150px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .tree-node:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .tree-node.active-user {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .tree-node.inactive-user {
            border-color: #dc3545;
            background: #fff5f5;
        }
        
        .tree-node.current-user {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .node-header {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .user-name {
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .tree-line {
            width: 2px;
            height: 30px;
            background-color: #dee2e6;
            margin: 0.5rem 0;
        }
        
        .tree-children {
            display: flex;
            gap: 2rem;
            position: relative;
        }
        
        .tree-children::before {
            content: "";
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 2rem);
            height: 2px;
            background-color: #dee2e6;
        }
        
        .tree-child {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }
        
        .tree-child::before {
            content: "";
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 15px;
            background-color: #dee2e6;
        }
        
        .child-label {
            font-size: 0.75rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .empty-slot {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            min-width: 150px;
            color: #6c757d;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .empty-slot:hover {
            border-color: #667eea;
            background: #f0f4ff;
            color: #667eea;
        }
        
        .empty-slot i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        @media (max-width: 768px) {
            .tree-container {
                padding: 1rem;
            }
            
            .tree-node {
                min-width: 120px;
                padding: 0.75rem;
                font-size: 0.8rem;
            }
            
            .tree-children {
                gap: 1rem;
            }
            
            .empty-slot {
                min-width: 120px;
                padding: 0.75rem;
            }
        }
        </style>';
    }
    
    /**
     * Generate JavaScript for tree interactions
     */
    public static function getTreeJS() {
        return '
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Tree node click handler
            document.querySelectorAll(".tree-node").forEach(function(node) {
                node.addEventListener("click", function() {
                    const userId = this.getAttribute("data-user-id");
                    if (userId) {
                        showUserDetails(userId);
                    }
                });
            });
            
            // Empty slot click handler
            document.querySelectorAll(".empty-slot").forEach(function(slot) {
                slot.addEventListener("click", function() {
                    const parentId = this.getAttribute("data-parent");
                    const side = this.getAttribute("data-side");
                    if (parentId && side) {
                        showPlacementForm(parentId, side);
                    }
                });
            });
        });
        
        function showUserDetails(userId) {
            // Show user details modal or redirect to user profile
            console.log("Show details for user:", userId);
            // You can implement modal or redirect logic here
        }
        
        function showPlacementForm(parentId, side) {
            // Show placement form for new user
            console.log("Show placement form for parent:", parentId, "side:", side);
            // You can implement placement form logic here
        }
        </script>';
    }
    
    /**
     * Render complete tree with CSS and JS
     */
    public static function renderCompleteTree($treeData, $currentUserId = null) {
        $html = self::getTreeCSS();
        $html .= self::renderTree($treeData, $currentUserId);
        $html .= self::getTreeJS();
        return $html;
    }
}
?>
