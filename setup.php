<?php
/**
 * MLM Binary Plan System - Database Setup Script
 * This script creates all necessary tables for the MLM system
 */

require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);
    
    echo "Database created/selected successfully.\n";
    
    // Create tables
    $tables = [
        // Admin table
        "CREATE TABLE IF NOT EXISTS admin (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // Franchise table
        "CREATE TABLE IF NOT EXISTS franchise (
            id INT PRIMARY KEY AUTO_INCREMENT,
            franchise_code VARCHAR(20) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            address TEXT,
            commission_rate DECIMAL(5,2) DEFAULT 5.00,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin(id)
        )",
        
        // Users table
        "CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            address TEXT,
            sponsor_id VARCHAR(20),
            franchise_id INT,
            placement_side ENUM('left', 'right'),
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (franchise_id) REFERENCES franchise(id),
            INDEX idx_sponsor (sponsor_id),
            INDEX idx_user_id (user_id)
        )",
        
        // Binary tree structure
        "CREATE TABLE IF NOT EXISTS binary_tree (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            parent_id VARCHAR(20),
            left_child VARCHAR(20),
            right_child VARCHAR(20),
            level INT DEFAULT 0,
            position ENUM('left', 'right', 'root'),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user (user_id),
            INDEX idx_parent (parent_id),
            INDEX idx_level (level)
        )",
        
        // Products table
        "CREATE TABLE IF NOT EXISTS products (
            id INT PRIMARY KEY AUTO_INCREMENT,
            product_code VARCHAR(20) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            pv_value DECIMAL(10,2) NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin(id)
        )"
    ];
    
        // PV Transactions table
        "CREATE TABLE IF NOT EXISTS pv_transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            transaction_type ENUM('purchase', 'bonus', 'manual') NOT NULL,
            pv_amount DECIMAL(10,2) NOT NULL,
            side ENUM('left', 'right') NOT NULL,
            product_id INT,
            reference_id VARCHAR(50),
            description TEXT,
            created_by_type ENUM('admin', 'franchise', 'system') DEFAULT 'system',
            created_by_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id),
            INDEX idx_user_side (user_id, side),
            INDEX idx_created_at (created_at)
        )",

        // Wallet table
        "CREATE TABLE IF NOT EXISTS wallet (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            balance DECIMAL(12,2) DEFAULT 0.00,
            total_earned DECIMAL(12,2) DEFAULT 0.00,
            total_withdrawn DECIMAL(12,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_wallet (user_id)
        )",

        // Wallet transactions
        "CREATE TABLE IF NOT EXISTS wallet_transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            transaction_type ENUM('credit', 'debit') NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            reference_type ENUM('pv_matching', 'withdrawal', 'bonus', 'manual') NOT NULL,
            reference_id VARCHAR(50),
            balance_before DECIMAL(12,2) NOT NULL,
            balance_after DECIMAL(12,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_type (user_id, transaction_type),
            INDEX idx_created_at (created_at)
        )",

        // Withdrawals table
        "CREATE TABLE IF NOT EXISTS withdrawals (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            bank_details JSON,
            status ENUM('pending', 'approved', 'rejected', 'processed') DEFAULT 'pending',
            admin_notes TEXT,
            processed_by INT,
            requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (processed_by) REFERENCES admin(id),
            INDEX idx_status (status),
            INDEX idx_user_status (user_id, status)
        )",

        // Income logs for PV matching
        "CREATE TABLE IF NOT EXISTS income_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            left_pv DECIMAL(10,2) NOT NULL,
            right_pv DECIMAL(10,2) NOT NULL,
            matched_pv DECIMAL(10,2) NOT NULL,
            income_amount DECIMAL(10,2) NOT NULL,
            capping_applied DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_left DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_right DECIMAL(10,2) DEFAULT 0.00,
            matching_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_date (user_id, matching_date),
            INDEX idx_matching_date (matching_date)
        )",

        // Login logs
        "CREATE TABLE IF NOT EXISTS login_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_type ENUM('admin', 'franchise', 'user') NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            logout_time TIMESTAMP NULL,
            status ENUM('success', 'failed') DEFAULT 'success',
            INDEX idx_user_type (user_type, user_id),
            INDEX idx_login_time (login_time)
        )",

        // System configuration
        "CREATE TABLE IF NOT EXISTS config (
            id INT PRIMARY KEY AUTO_INCREMENT,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value TEXT,
            description TEXT,
            updated_by INT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES admin(id)
        )",

        // Purchase orders
        "CREATE TABLE IF NOT EXISTS purchase_orders (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_id VARCHAR(30) UNIQUE NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            product_id INT NOT NULL,
            quantity INT DEFAULT 1,
            total_amount DECIMAL(10,2) NOT NULL,
            pv_amount DECIMAL(10,2) NOT NULL,
            placement_side ENUM('left', 'right') NOT NULL,
            payment_method ENUM('razorpay', 'manual') DEFAULT 'razorpay',
            payment_id VARCHAR(100),
            payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
            order_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id),
            INDEX idx_user_status (user_id, order_status),
            INDEX idx_payment_status (payment_status)
        )",

        // System logs
        "CREATE TABLE IF NOT EXISTS system_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            log_type ENUM('info', 'warning', 'error', 'cron') NOT NULL,
            message TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_log_type (log_type),
            INDEX idx_created_at (created_at)
        )"
    ];

    foreach ($tables as $sql) {
        $pdo->exec($sql);
        echo "Table created successfully.\n";
    }

    // Insert default configuration
    $defaultConfigs = [
        ['pv_rate', '0.10', 'PV to INR conversion rate (1 PV = ₹0.10)'],
        ['daily_capping', '130000.00', 'Maximum daily income per user in INR'],
        ['min_withdrawal', '500.00', 'Minimum withdrawal amount in INR'],
        ['razorpay_mode', 'test', 'Razorpay mode: test or live'],
        ['company_name', 'ShaktiPure MLM', 'Company name'],
        ['support_email', '<EMAIL>', 'Support email address'],
        ['support_phone', '+91-9999999999', 'Support phone number']
    ];

    $configStmt = $pdo->prepare("INSERT IGNORE INTO config (config_key, config_value, description) VALUES (?, ?, ?)");
    foreach ($defaultConfigs as $config) {
        $configStmt->execute($config);
    }

    // Create default admin user
    $adminPassword = password_hash('admin123', PASSWORD_BCRYPT);
    $adminStmt = $pdo->prepare("INSERT IGNORE INTO admin (username, email, password, full_name, phone) VALUES (?, ?, ?, ?, ?)");
    $adminStmt->execute(['admin', '<EMAIL>', $adminPassword, 'System Administrator', '+91-9999999999']);

    echo "Database setup completed successfully!\n";
    echo "Default admin credentials:\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    echo "Please change the default password after first login.\n";

} catch (PDOException $e) {
    die("Database setup failed: " . $e->getMessage());
}
?>
