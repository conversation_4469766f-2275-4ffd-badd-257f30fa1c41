<?php
/**
 * User Dashboard
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/BinaryTree.php';
require_once '../includes/TreeVisualization.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Initialize classes
$binaryTree = new BinaryTree();
$pvSystem = new PVSystem();
$wallet = new Wallet();

// Get user data
$db = Database::getInstance();
$userStmt = $db->prepare("SELECT * FROM users WHERE user_id = ?");
$userStmt->execute([$userId]);
$userDetails = $userStmt->fetch();

// Get tree statistics
$treeStats = $binaryTree->getTreeStats($userId);

// Get PV data
$ownPV = $pvSystem->getUserPVTotals($userId);
$downlinePV = $pvSystem->getDownlinePVTotals($userId);

// Get wallet data
$walletData = $wallet->getWallet($userId);

// Get recent transactions
$recentPV = $pvSystem->getPVHistory($userId, 5);
$recentWallet = $wallet->getTransactionHistory($userId, 5);

// Get tree structure for visualization
$treeData = $binaryTree->getTreeStructure($userId, 3);

// Calculate total PV including downline
$totalLeftPV = $ownPV['left_pv'] + $downlinePV['left_pv'];
$totalRightPV = $ownPV['right_pv'] + $downlinePV['right_pv'];
$matchingPV = min($totalLeftPV, $totalRightPV);

// Get referral link
$referralLink = SITE_URL . '/user/register.php?sponsor=' . urlencode($userId);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-network-wired me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tree.php">
                            <i class="fas fa-sitemap me-1"></i>Binary Tree
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-cart me-1"></i>Products
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <div class="card-body text-center">
                        <h2 class="mb-3">Welcome back, <?php echo htmlspecialchars($currentUser['full_name']); ?>!</h2>
                        <p class="mb-0">User ID: <strong><?php echo htmlspecialchars($userId); ?></strong></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo formatCurrency($walletData['balance']); ?></h4>
                            <small class="text-muted">Wallet Balance</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo formatPV($matchingPV); ?></h4>
                            <small class="text-muted">Matching PV</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo $treeStats['total_downline']; ?></h4>
                            <small class="text-muted">Total Downline</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon info me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo formatCurrency($walletData['total_earned']); ?></h4>
                            <small class="text-muted">Total Earned</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PV Overview -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>PV Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="pv-display mb-3" style="background: linear-gradient(135deg, #28a745, #20c997);">
                                        <div class="pv-value"><?php echo formatPV($totalLeftPV); ?></div>
                                        <div>Left PV</div>
                                    </div>
                                    <small class="text-muted">
                                        Own: <?php echo formatPV($ownPV['left_pv']); ?><br>
                                        Downline: <?php echo formatPV($downlinePV['left_pv']); ?>
                                    </small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="pv-display mb-3" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                                        <div class="pv-value"><?php echo formatPV($totalRightPV); ?></div>
                                        <div>Right PV</div>
                                    </div>
                                    <small class="text-muted">
                                        Own: <?php echo formatPV($ownPV['right_pv']); ?><br>
                                        Downline: <?php echo formatPV($downlinePV['right_pv']); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h6>Matching PV: <span class="text-success"><?php echo formatPV($matchingPV); ?></span></h6>
                            <small class="text-muted">Potential Income: <?php echo formatCurrency($matchingPV * APP_PV_RATE); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>Referral Link</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Share this link to invite new members:</p>
                        <div class="input-group">
                            <input type="text" class="form-control" id="referralLink" value="<?php echo htmlspecialchars($referralLink); ?>" readonly>
                            <button class="btn btn-outline-primary" type="button" onclick="copyReferralLink()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="mt-3">
                            <a href="https://wa.me/?text=<?php echo urlencode('Join our MLM network: ' . $referralLink); ?>" 
                               class="btn btn-success btn-sm me-2" target="_blank">
                                <i class="fab fa-whatsapp me-1"></i>Share on WhatsApp
                            </a>
                            <a href="mailto:?subject=Join Our Network&body=<?php echo urlencode('Join our MLM network: ' . $referralLink); ?>" 
                               class="btn btn-info btn-sm">
                                <i class="fas fa-envelope me-1"></i>Share via Email
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent PV Transactions</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentPV)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>PV</th>
                                            <th>Side</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentPV as $pv): ?>
                                            <tr>
                                                <td><?php echo date('M d', strtotime($pv['created_at'])); ?></td>
                                                <td><?php echo formatPV($pv['pv_amount']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $pv['side'] === 'left' ? 'success' : 'warning'; ?>">
                                                        <?php echo ucfirst($pv['side']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo ucfirst($pv['transaction_type']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="pv-history.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No PV transactions yet</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Recent Wallet Activity</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentWallet)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentWallet as $trans): ?>
                                            <tr>
                                                <td><?php echo date('M d', strtotime($trans['created_at'])); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $trans['transaction_type'] === 'credit' ? 'success' : 'danger'; ?>">
                                                        <?php echo ucfirst($trans['transaction_type']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatCurrency($trans['amount']); ?></td>
                                                <td class="small"><?php echo htmlspecialchars(substr($trans['description'], 0, 20)); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="wallet.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No wallet activity yet</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyReferralLink() {
            const linkInput = document.getElementById('referralLink');
            linkInput.select();
            linkInput.setSelectionRange(0, 99999);
            document.execCommand('copy');
            
            // Show feedback
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.add('btn-success');
            button.classList.remove('btn-outline-primary');
            
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-primary');
            }, 2000);
        }
    </script>
</body>
</html>
