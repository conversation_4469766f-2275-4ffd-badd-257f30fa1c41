<?php
/**
 * Admin Dashboard
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$wallet = new Wallet();

// Get statistics
$db = Database::getInstance();

// User statistics
$userStats = $db->query("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users,
        SUM(CASE WHEN DATE(registration_date) = CURDATE() THEN 1 ELSE 0 END) as today_registrations
    FROM users
")->fetch();

// Wallet statistics
$walletStats = $wallet->getWalletStatistics();

// PV statistics
$pvStats = $pvSystem->getPVStatistics();

// Withdrawal statistics
$withdrawalStats = $db->query("
    SELECT 
        COUNT(*) as total_withdrawals,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_withdrawals,
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount
    FROM withdrawals
")->fetch();

// Recent activities
$recentUsers = $db->query("SELECT user_id, full_name, email, registration_date FROM users ORDER BY registration_date DESC LIMIT 5")->fetchAll();
$recentWithdrawals = $db->query("
    SELECT w.*, u.full_name 
    FROM withdrawals w 
    JOIN users u ON w.user_id = u.user_id 
    ORDER BY w.requested_at DESC 
    LIMIT 5
")->fetchAll();

// System logs
$systemLogs = $db->query("SELECT * FROM system_logs ORDER BY created_at DESC LIMIT 5")->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i><?php echo SITE_NAME; ?> Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-1"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="franchises.php">
                            <i class="fas fa-store me-1"></i>Franchises
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-1"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="withdrawals.php">
                            <i class="fas fa-money-bill-wave me-1"></i>Withdrawals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar me-1"></i>Reports
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <div class="card-body">
                        <h2 class="mb-3">Welcome to Admin Panel</h2>
                        <p class="mb-0">Manage your MLM system efficiently</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($userStats['total_users']); ?></h4>
                            <small class="text-muted">Total Users</small>
                            <div class="mt-1">
                                <small class="text-success">
                                    <i class="fas fa-arrow-up me-1"></i><?php echo $userStats['today_registrations']; ?> today
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo formatCurrency($walletStats['total_balance']); ?></h4>
                            <small class="text-muted">Total Wallet Balance</small>
                            <div class="mt-1">
                                <small class="text-info">
                                    <?php echo number_format($walletStats['total_wallets']); ?> wallets
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo formatPV($pvStats['total_pv']); ?></h4>
                            <small class="text-muted">Total PV</small>
                            <div class="mt-1">
                                <small class="text-info">
                                    <?php echo number_format($pvStats['total_transactions']); ?> transactions
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon info me-3">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo $withdrawalStats['pending_withdrawals']; ?></h4>
                            <small class="text-muted">Pending Withdrawals</small>
                            <div class="mt-1">
                                <small class="text-warning">
                                    <?php echo formatCurrency($withdrawalStats['pending_amount']); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>User Status Distribution</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="mb-3">
                                    <h3 class="text-success"><?php echo $userStats['active_users']; ?></h3>
                                    <small class="text-muted">Active Users</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mb-3">
                                    <h3 class="text-danger"><?php echo $userStats['inactive_users']; ?></h3>
                                    <small class="text-muted">Inactive Users</small>
                                </div>
                            </div>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <?php 
                            $activePercentage = $userStats['total_users'] > 0 ? ($userStats['active_users'] / $userStats['total_users']) * 100 : 0;
                            ?>
                            <div class="progress-bar bg-success" style="width: <?php echo $activePercentage; ?>%"></div>
                        </div>
                        <small class="text-muted"><?php echo number_format($activePercentage, 1); ?>% Active</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Financial Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Total Earned</small>
                                <h5 class="text-success"><?php echo formatCurrency($walletStats['total_earned']); ?></h5>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Total Withdrawn</small>
                                <h5 class="text-warning"><?php echo formatCurrency($walletStats['total_withdrawn']); ?></h5>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Approved Withdrawals</small>
                                <h6 class="text-info"><?php echo formatCurrency($withdrawalStats['approved_amount']); ?></h6>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Pending Amount</small>
                                <h6 class="text-danger"><?php echo formatCurrency($withdrawalStats['pending_amount']); ?></h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Recent Registrations</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentUsers)): ?>
                            <?php foreach ($recentUsers as $user): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <i class="fas fa-user-circle fa-2x text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($user['full_name']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($user['user_id']); ?></small>
                                        <br>
                                        <small class="text-muted"><?php echo timeAgo($user['registration_date']); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            <div class="text-center">
                                <a href="users.php" class="btn btn-sm btn-outline-primary">View All Users</a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No recent registrations</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Recent Withdrawals</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentWithdrawals)): ?>
                            <?php foreach ($recentWithdrawals as $withdrawal): ?>
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($withdrawal['full_name']); ?></h6>
                                        <small class="text-muted"><?php echo formatCurrency($withdrawal['amount']); ?></small>
                                    </div>
                                    <div>
                                        <?php
                                        $statusClass = '';
                                        switch ($withdrawal['status']) {
                                            case 'pending': $statusClass = 'warning'; break;
                                            case 'approved': $statusClass = 'success'; break;
                                            case 'rejected': $statusClass = 'danger'; break;
                                        }
                                        ?>
                                        <span class="badge bg-<?php echo $statusClass; ?>">
                                            <?php echo ucfirst($withdrawal['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            <div class="text-center">
                                <a href="withdrawals.php" class="btn btn-sm btn-outline-primary">View All Withdrawals</a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No recent withdrawals</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>System Logs</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($systemLogs)): ?>
                            <?php foreach ($systemLogs as $log): ?>
                                <div class="mb-3">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-<?php echo $log['log_type'] === 'error' ? 'danger' : ($log['log_type'] === 'warning' ? 'warning' : 'info'); ?> me-2">
                                            <?php echo ucfirst($log['log_type']); ?>
                                        </span>
                                        <small class="text-muted"><?php echo timeAgo($log['created_at']); ?></small>
                                    </div>
                                    <p class="mb-0 small"><?php echo htmlspecialchars(substr($log['message'], 0, 60)); ?>...</p>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted text-center">No system logs</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
