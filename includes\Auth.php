<?php
/**
 * Authentication Middleware Class
 * MLM Binary Plan System
 */

class Auth {
    
    /**
     * Check if user is authenticated
     */
    public static function check($userType = null) {
        return isLoggedIn($userType);
    }
    
    /**
     * Get current authenticated user
     */
    public static function user() {
        return getCurrentUser();
    }
    
    /**
     * Get current user ID
     */
    public static function id() {
        return getCurrentUserId();
    }
    
    /**
     * Get current user type
     */
    public static function userType() {
        return getCurrentUserType();
    }
    
    /**
     * Require authentication for specific user type
     */
    public static function require($userType = null) {
        if (!self::check($userType)) {
            $loginPage = $userType ? "{$userType}/login.php" : 'login.php';
            Response::redirect($loginPage);
        }
    }
    
    /**
     * Require admin authentication
     */
    public static function requireAdmin() {
        self::require('admin');
    }
    
    /**
     * Require franchise authentication
     */
    public static function requireFranchise() {
        self::require('franchise');
    }
    
    /**
     * Require user authentication
     */
    public static function requireUser() {
        self::require('user');
    }
    
    /**
     * Check if current user is admin
     */
    public static function isAdmin() {
        return self::userType() === 'admin';
    }
    
    /**
     * Check if current user is franchise
     */
    public static function isFranchise() {
        return self::userType() === 'franchise';
    }
    
    /**
     * Check if current user is regular user
     */
    public static function isUser() {
        return self::userType() === 'user';
    }
    
    /**
     * Login user
     */
    public static function login($userType, $userId, $userData = []) {
        setUserSession($userType, $userId, $userData);
        
        // Log successful login
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("INSERT INTO login_logs (user_type, user_id, ip_address, user_agent, status) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                $userType,
                $userId,
                getUserIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                'success'
            ]);
        } catch (Exception $e) {
            error_log("Failed to log login: " . $e->getMessage());
        }
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        $userType = self::userType();
        $userId = self::id();
        
        // Log logout
        if ($userType && $userId) {
            try {
                $db = Database::getInstance();
                $stmt = $db->prepare("UPDATE login_logs SET logout_time = NOW() WHERE user_type = ? AND user_id = ? AND logout_time IS NULL ORDER BY login_time DESC LIMIT 1");
                $stmt->execute([$userType, $userId]);
            } catch (Exception $e) {
                error_log("Failed to log logout: " . $e->getMessage());
            }
        }
        
        destroyUserSession();
        return $userType;
    }
    
    /**
     * Attempt login with credentials
     */
    public static function attempt($userType, $credentials) {
        try {
            $db = Database::getInstance();
            $table = $userType === 'user' ? 'users' : $userType;
            
            // Prepare query based on user type
            if ($userType === 'user') {
                $stmt = $db->prepare("SELECT id, user_id, username, email, password, full_name, phone, status FROM users WHERE username = ? OR email = ? OR user_id = ?");
                $stmt->execute([$credentials['username'], $credentials['username'], $credentials['username']]);
            } else {
                $stmt = $db->prepare("SELECT id, username, email, password, full_name, phone, status FROM {$table} WHERE username = ? OR email = ?");
                $stmt->execute([$credentials['username'], $credentials['username']]);
            }
            
            $user = $stmt->fetch();
            
            if ($user && password_verify($credentials['password'], $user['password'])) {
                if ($user['status'] === 'active') {
                    // Prepare user data for session
                    $userData = [
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'full_name' => $user['full_name'],
                        'phone' => $user['phone']
                    ];
                    
                    if ($userType === 'user') {
                        $userData['id'] = $user['id'];
                        self::login($userType, $user['user_id'], $userData);
                    } else {
                        self::login($userType, $user['id'], $userData);
                    }
                    
                    return true;
                } else {
                    return ['error' => 'Account is ' . $user['status']];
                }
            } else {
                // Log failed attempt
                if ($user) {
                    try {
                        $logStmt = $db->prepare("INSERT INTO login_logs (user_type, user_id, ip_address, user_agent, status) VALUES (?, ?, ?, ?, ?)");
                        $userId = $userType === 'user' ? $user['user_id'] : $user['id'];
                        $logStmt->execute([$userType, $userId, getUserIP(), $_SERVER['HTTP_USER_AGENT'] ?? '', 'failed']);
                    } catch (Exception $e) {
                        error_log("Failed to log failed attempt: " . $e->getMessage());
                    }
                }
                return ['error' => 'Invalid credentials'];
            }
        } catch (Exception $e) {
            error_log("Auth attempt error: " . $e->getMessage());
            return ['error' => 'Authentication failed'];
        }
    }
    
    /**
     * Get user data from database
     */
    public static function getUserData($userType, $userId) {
        try {
            $db = Database::getInstance();
            
            if ($userType === 'user') {
                $stmt = $db->prepare("SELECT * FROM users WHERE user_id = ?");
            } else {
                $table = $userType === 'admin' ? 'admin' : 'franchise';
                $stmt = $db->prepare("SELECT * FROM {$table} WHERE id = ?");
            }
            
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Get user data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Check if user has permission
     */
    public static function hasPermission($permission) {
        $userType = self::userType();
        
        // Admin has all permissions
        if ($userType === 'admin') {
            return true;
        }
        
        // Define permissions for each user type
        $permissions = [
            'franchise' => [
                'manage_users',
                'assign_products',
                'view_commissions'
            ],
            'user' => [
                'view_dashboard',
                'purchase_products',
                'request_withdrawal'
            ]
        ];
        
        return in_array($permission, $permissions[$userType] ?? []);
    }
}
?>
