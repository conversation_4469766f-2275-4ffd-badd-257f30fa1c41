<?php
/**
 * PV (Point Value) System Class
 * MLM Binary Plan System
 * Handles PV transactions, matching, and income calculation
 */

class PVSystem {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
    }
    
    /**
     * Add PV to user's account
     */
    public function addPV($userId, $pvAmount, $side, $transactionType = 'purchase', $productId = null, $referenceId = null, $description = '', $createdByType = 'system', $createdById = null) {
        try {
            $this->db->beginTransaction();
            
            // Insert PV transaction
            $stmt = $this->db->prepare("INSERT INTO pv_transactions (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, created_by_type, created_by_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $side,
                $productId,
                $referenceId,
                $description,
                $createdByType,
                $createdById
            ]);
            
            // Trigger PV matching for user's upline
            $this->triggerPVMatching($userId);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Add PV error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's PV totals
     */
    public function getUserPVTotals($userId) {
        $stmt = $this->db->prepare("
            SELECT 
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                SUM(pv_amount) as total_pv
            FROM pv_transactions 
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        
        return [
            'left_pv' => (float) ($result['left_pv'] ?? 0),
            'right_pv' => (float) ($result['right_pv'] ?? 0),
            'total_pv' => (float) ($result['total_pv'] ?? 0)
        ];
    }
    
    /**
     * Get user's downline PV totals
     */
    public function getDownlinePVTotals($userId) {
        $binaryTree = new BinaryTree();
        $leftLeg = $binaryTree->getLeftLeg($userId);
        $rightLeg = $binaryTree->getRightLeg($userId);
        
        $leftPV = 0;
        $rightPV = 0;
        
        // Calculate left leg PV
        if (!empty($leftLeg)) {
            $leftUserIds = "'" . implode("','", $leftLeg) . "'";
            $leftStmt = $this->db->query("SELECT SUM(pv_amount) as total FROM pv_transactions WHERE user_id IN ($leftUserIds)");
            $leftResult = $leftStmt->fetch();
            $leftPV = (float) ($leftResult['total'] ?? 0);
        }
        
        // Calculate right leg PV
        if (!empty($rightLeg)) {
            $rightUserIds = "'" . implode("','", $rightLeg) . "'";
            $rightStmt = $this->db->query("SELECT SUM(pv_amount) as total FROM pv_transactions WHERE user_id IN ($rightUserIds)");
            $rightResult = $rightStmt->fetch();
            $rightPV = (float) ($rightResult['total'] ?? 0);
        }
        
        return [
            'left_pv' => $leftPV,
            'right_pv' => $rightPV,
            'total_pv' => $leftPV + $rightPV
        ];
    }
    
    /**
     * Trigger PV matching for user's upline
     */
    public function triggerPVMatching($userId) {
        $binaryTree = new BinaryTree();
        $upline = $binaryTree->getUpline($userId);
        
        // Process matching for each upline member
        foreach ($upline as $uplineUserId) {
            $this->processPVMatching($uplineUserId);
        }
    }
    
    /**
     * Process PV matching for a specific user
     */
    public function processPVMatching($userId) {
        try {
            // Get current PV totals including downline
            $ownPV = $this->getUserPVTotals($userId);
            $downlinePV = $this->getDownlinePVTotals($userId);
            
            $totalLeftPV = $ownPV['left_pv'] + $downlinePV['left_pv'];
            $totalRightPV = $ownPV['right_pv'] + $downlinePV['right_pv'];
            
            // Get previous matching data
            $lastMatching = $this->getLastMatching($userId);
            $carriedLeftPV = $lastMatching['carry_forward_left'] ?? 0;
            $carriedRightPV = $lastMatching['carry_forward_right'] ?? 0;
            
            // Add carried forward PV
            $totalLeftPV += $carriedLeftPV;
            $totalRightPV += $carriedRightPV;
            
            // Calculate matching PV (minimum of left and right)
            $matchedPV = min($totalLeftPV, $totalRightPV);
            
            if ($matchedPV > 0) {
                // Calculate income (1 PV = configured rate)
                $pvRate = $this->config->getPVRate();
                $incomeAmount = $matchedPV * $pvRate;
                
                // Apply daily capping
                $dailyCapping = $this->config->getDailyCapping();
                $todayIncome = $this->getTodayIncome($userId);
                $availableCapping = $dailyCapping - $todayIncome;
                
                $cappingApplied = 0;
                if ($incomeAmount > $availableCapping) {
                    $cappingApplied = $incomeAmount - $availableCapping;
                    $incomeAmount = $availableCapping;
                }
                
                // Calculate carry forward
                $carryForwardLeft = $totalLeftPV - $matchedPV;
                $carryForwardRight = $totalRightPV - $matchedPV;
                
                // Record income log
                $this->recordIncomeLog($userId, $totalLeftPV, $totalRightPV, $matchedPV, $incomeAmount, $cappingApplied, $carryForwardLeft, $carryForwardRight);
                
                // Credit income to wallet
                if ($incomeAmount > 0) {
                    $this->creditWallet($userId, $incomeAmount, 'PV Matching Income');
                }
            }
            
        } catch (Exception $e) {
            error_log("PV matching error for user $userId: " . $e->getMessage());
        }
    }
    
    /**
     * Get last matching record for user
     */
    public function getLastMatching($userId) {
        $stmt = $this->db->prepare("SELECT * FROM income_logs WHERE user_id = ? ORDER BY matching_date DESC LIMIT 1");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }
    
    /**
     * Get today's income for user
     */
    public function getTodayIncome($userId) {
        $today = date('Y-m-d');
        $stmt = $this->db->prepare("SELECT SUM(income_amount) as total FROM income_logs WHERE user_id = ? AND matching_date = ?");
        $stmt->execute([$userId, $today]);
        $result = $stmt->fetch();
        return (float) ($result['total'] ?? 0);
    }
    
    /**
     * Record income log
     */
    public function recordIncomeLog($userId, $leftPV, $rightPV, $matchedPV, $incomeAmount, $cappingApplied, $carryForwardLeft, $carryForwardRight) {
        $stmt = $this->db->prepare("INSERT INTO income_logs (user_id, left_pv, right_pv, matched_pv, income_amount, capping_applied, carry_forward_left, carry_forward_right, matching_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $userId,
            $leftPV,
            $rightPV,
            $matchedPV,
            $incomeAmount,
            $cappingApplied,
            $carryForwardLeft,
            $carryForwardRight,
            date('Y-m-d')
        ]);
    }
    
    /**
     * Credit amount to user's wallet
     */
    public function creditWallet($userId, $amount, $description) {
        try {
            $this->db->beginTransaction();
            
            // Get current wallet balance
            $walletStmt = $this->db->prepare("SELECT balance FROM wallet WHERE user_id = ?");
            $walletStmt->execute([$userId]);
            $wallet = $walletStmt->fetch();
            
            if (!$wallet) {
                // Create wallet if doesn't exist
                $createStmt = $this->db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0, 0, 0)");
                $createStmt->execute([$userId]);
                $balanceBefore = 0;
            } else {
                $balanceBefore = $wallet['balance'];
            }
            
            $balanceAfter = $balanceBefore + $amount;
            
            // Update wallet
            $updateStmt = $this->db->prepare("UPDATE wallet SET balance = balance + ?, total_earned = total_earned + ? WHERE user_id = ?");
            $updateStmt->execute([$amount, $amount, $userId]);
            
            // Record wallet transaction
            $transStmt = $this->db->prepare("INSERT INTO wallet_transactions (user_id, transaction_type, amount, description, reference_type, balance_before, balance_after) VALUES (?, 'credit', ?, ?, 'pv_matching', ?, ?)");
            $transStmt->execute([$userId, $amount, $description, $balanceBefore, $balanceAfter]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Credit wallet error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get PV transaction history
     */
    public function getPVHistory($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT pt.*, p.name as product_name 
            FROM pv_transactions pt 
            LEFT JOIN products p ON pt.product_id = p.id 
            WHERE pt.user_id = ? 
            ORDER BY pt.created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get income history
     */
    public function getIncomeHistory($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("SELECT * FROM income_logs WHERE user_id = ? ORDER BY matching_date DESC LIMIT ? OFFSET ?");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get PV statistics for admin
     */
    public function getPVStatistics($dateFrom = null, $dateTo = null) {
        $whereClause = '';
        $params = [];
        
        if ($dateFrom && $dateTo) {
            $whereClause = 'WHERE DATE(created_at) BETWEEN ? AND ?';
            $params = [$dateFrom, $dateTo];
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_transactions,
                SUM(pv_amount) as total_pv,
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                AVG(pv_amount) as avg_pv
            FROM pv_transactions 
            $whereClause
        ");
        $stmt->execute($params);
        return $stmt->fetch();
    }
    
    /**
     * Run daily PV matching for all users
     */
    public function runDailyMatching() {
        try {
            $stmt = $this->db->prepare("SELECT DISTINCT user_id FROM users WHERE status = 'active'");
            $stmt->execute();
            $users = $stmt->fetchAll();
            
            $processed = 0;
            foreach ($users as $user) {
                $this->processPVMatching($user['user_id']);
                $processed++;
            }
            
            return $processed;
            
        } catch (Exception $e) {
            error_log("Daily matching error: " . $e->getMessage());
            return false;
        }
    }
}
?>
